import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { ApiClient, User, LoginCredentials, RegisterData, TokenManager } from '../config/api';
import { toast } from 'sonner';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  updateUser: (userData: Partial<User>) => void;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user && TokenManager.isAuthenticated();

  // Initialize auth state on app load
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        if (TokenManager.isAuthenticated()) {
          const userData = await ApiClient.getProfile();
          setUser(userData);
        }
      } catch (error) {
        console.error('Failed to initialize auth:', error);
        TokenManager.clearTokens();
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (credentials: LoginCredentials): Promise<void> => {
    try {
      setIsLoading(true);
      const { user: userData } = await ApiClient.login(credentials);
      setUser(userData);
      toast.success('Zalogowano pomyślnie!');
    } catch (error: any) {
      const message = error.response?.data?.message || 'Błąd logowania';
      toast.error(message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (data: RegisterData): Promise<void> => {
    try {
      setIsLoading(true);
      const { user: userData } = await ApiClient.register(data);
      setUser(userData);
      toast.success('Konto utworzone pomyślnie!');
    } catch (error: any) {
      const message = error.response?.data?.message || 'Błąd rejestracji';
      toast.error(message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    try {
      await ApiClient.logout();
      setUser(null);
      toast.success('Wylogowano pomyślnie');
    } catch (error) {
      console.error('Logout error:', error);
      // Still clear local state even if API call fails
      setUser(null);
      TokenManager.clearTokens();
    }
  };

  const updateUser = (userData: Partial<User>): void => {
    if (user) {
      setUser({ ...user, ...userData });
    }
  };

  const refreshUser = async (): Promise<void> => {
    try {
      if (TokenManager.isAuthenticated()) {
        const userData = await ApiClient.getProfile();
        setUser(userData);
      }
    } catch (error) {
      console.error('Failed to refresh user:', error);
      // If refresh fails, user might need to re-login
      setUser(null);
      TokenManager.clearTokens();
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    register,
    logout,
    updateUser,
    refreshUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Higher-order component for protected routes
export const withAuth = <P extends object>(Component: React.ComponentType<P>) => {
  return (props: P) => {
    const { isAuthenticated, isLoading } = useAuth();

    if (isLoading) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      );
    }

    if (!isAuthenticated) {
      // Redirect to login page
      window.location.href = '/login';
      return null;
    }

    return <Component {...props} />;
  };
};

// Hook for checking if user has specific subscription plan
export const useSubscription = () => {
  const { user } = useAuth();

  const hasSubscription = (requiredPlan: string | string[]): boolean => {
    if (!user) return false;

    const plans = Array.isArray(requiredPlan) ? requiredPlan : [requiredPlan];
    return plans.includes(user.subscriptionPlan);
  };

  const isPro = (): boolean => {
    return hasSubscription(['pro', 'business']);
  };

  const isBusiness = (): boolean => {
    return hasSubscription('business');
  };

  const isFree = (): boolean => {
    return hasSubscription('free');
  };

  return {
    currentPlan: user?.subscriptionPlan || 'free',
    hasSubscription,
    isPro,
    isBusiness,
    isFree,
  };
};
