import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api/v1';

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Token management
class TokenManager {
  private static readonly ACCESS_TOKEN_KEY = 'access_token';
  private static readonly REFRESH_TOKEN_KEY = 'refresh_token';

  static getAccessToken(): string | null {
    return localStorage.getItem(this.ACCESS_TOKEN_KEY);
  }

  static getRefreshToken(): string | null {
    return localStorage.getItem(this.REFRESH_TOKEN_KEY);
  }

  static setTokens(accessToken: string, refreshToken: string): void {
    localStorage.setItem(this.ACCESS_TOKEN_KEY, accessToken);
    localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);
  }

  static clearTokens(): void {
    localStorage.removeItem(this.ACCESS_TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
  }

  static isAuthenticated(): boolean {
    return !!this.getAccessToken();
  }
}

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    const token = TokenManager.getAccessToken();
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = TokenManager.getRefreshToken();
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {
            refreshToken,
          });

          const { accessToken, refreshToken: newRefreshToken } = response.data.data.tokens;
          TokenManager.setTokens(accessToken, newRefreshToken);

          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${accessToken}`;
          return apiClient(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        TokenManager.clearTokens();
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  errors?: any[];
}

export interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// User types
export interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  subscriptionPlan: string;
  isVerified: boolean;
  createdAt: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
}

export interface AuthResponse {
  user: User;
  tokens: {
    accessToken: string;
    refreshToken: string;
  };
}

// Conversion types
export interface Conversion {
  id: string;
  fileName: string;
  fileSize: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  createdAt: string;
  processingTime?: number;
  resultPath?: string;
}

export interface ConversionOptions {
  template?: string;
  outputFormat?: 'xlsx' | 'csv';
  extractTables?: boolean;
  extractText?: boolean;
  ocrEnabled?: boolean;
}

// Usage types
export interface UsageStats {
  currentMonth: {
    conversions: number;
    totalFileSize: number;
    totalProcessingTime: number;
  };
  allTime: {
    conversions: number;
    totalFileSize: number;
    totalProcessingTime: number;
  };
  limits: {
    conversions: number;
    fileSize: number;
  };
}

// Subscription types
export interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  currency: string;
  interval: string;
  features: string[];
  limits: {
    conversions: number;
    fileSize: number;
  };
}

export interface Subscription {
  plan: string;
  status: string;
  currentPeriodEnd?: string;
  cancelAtPeriodEnd?: boolean;
}

// API Client class
export class ApiClient {
  // Auth endpoints
  static async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await apiClient.post<ApiResponse<AuthResponse>>('/auth/login', credentials);
    const { user, tokens } = response.data.data!;
    TokenManager.setTokens(tokens.accessToken, tokens.refreshToken);
    return { user, tokens };
  }

  static async register(data: RegisterData): Promise<AuthResponse> {
    const response = await apiClient.post<ApiResponse<AuthResponse>>('/auth/register', data);
    const { user, tokens } = response.data.data!;
    TokenManager.setTokens(tokens.accessToken, tokens.refreshToken);
    return { user, tokens };
  }

  static async logout(): Promise<void> {
    const refreshToken = TokenManager.getRefreshToken();
    if (refreshToken) {
      try {
        await apiClient.post('/auth/logout', { refreshToken });
      } catch (error) {
        // Ignore logout errors
      }
    }
    TokenManager.clearTokens();
  }

  static async forgotPassword(email: string): Promise<void> {
    await apiClient.post('/auth/forgot-password', { email });
  }

  static async resetPassword(token: string, password: string): Promise<void> {
    await apiClient.post('/auth/reset-password', { token, password });
  }

  // User endpoints
  static async getProfile(): Promise<User> {
    const response = await apiClient.get<ApiResponse<{ user: User }>>('/users/profile');
    return response.data.data!.user;
  }

  static async updateProfile(data: Partial<User>): Promise<User> {
    const response = await apiClient.put<ApiResponse<{ user: User }>>('/users/profile', data);
    return response.data.data!.user;
  }

  static async getUsageStats(): Promise<UsageStats> {
    const response = await apiClient.get<ApiResponse<{ usage: UsageStats }>>('/users/usage');
    return response.data.data!.usage;
  }

  // Conversion endpoints
  static async uploadFile(file: File, options?: ConversionOptions): Promise<Conversion> {
    const formData = new FormData();
    formData.append('pdf', file);
    if (options) {
      formData.append('options', JSON.stringify(options));
    }

    const response = await apiClient.post<ApiResponse<{ conversion: Conversion }>>(
      '/conversions/upload',
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return response.data.data!.conversion;
  }

  static async getConversions(page = 1, limit = 10, status?: string): Promise<PaginatedResponse<Conversion>> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });
    if (status) {
      params.append('status', status);
    }

    const response = await apiClient.get<ApiResponse<{ conversions: Conversion[]; pagination: any }>>(
      `/conversions?${params}`
    );
    return {
      items: response.data.data!.conversions,
      pagination: response.data.data!.pagination,
    };
  }

  static async getConversion(id: string): Promise<Conversion> {
    const response = await apiClient.get<ApiResponse<{ conversion: Conversion }>>(`/conversions/${id}`);
    return response.data.data!.conversion;
  }

  static async downloadConversion(id: string): Promise<Blob> {
    const response = await apiClient.get(`/conversions/${id}/download`, {
      responseType: 'blob',
    });
    return response.data;
  }

  static async deleteConversion(id: string): Promise<void> {
    await apiClient.delete(`/conversions/${id}`);
  }

  static async retryConversion(id: string): Promise<Conversion> {
    const response = await apiClient.post<ApiResponse<{ conversion: Conversion }>>(`/conversions/${id}/retry`);
    return response.data.data!.conversion;
  }

  // Subscription endpoints
  static async getSubscriptionPlans(): Promise<SubscriptionPlan[]> {
    const response = await apiClient.get<ApiResponse<{ plans: SubscriptionPlan[] }>>('/subscriptions/plans');
    return response.data.data!.plans;
  }

  static async getCurrentSubscription(): Promise<Subscription> {
    const response = await apiClient.get<ApiResponse<{ subscription: Subscription }>>('/subscriptions/current');
    return response.data.data!.subscription;
  }
}

export { TokenManager, apiClient };
export default ApiClient;
