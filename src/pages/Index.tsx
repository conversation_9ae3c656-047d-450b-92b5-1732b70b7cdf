
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { FileText, Zap, Shield, Clock, ArrowRight, Star } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import FeatureComparison from '@/components/FeatureComparison';
import { useAuth } from '@/contexts/AuthContext';

const Index = () => {
  const { isAuthenticated } = useAuth();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Hero Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
        <div className="text-center">
          <h1 className="text-4xl md:text-6xl font-extrabold text-gray-900 mb-6">
            Konwertuj PDF do Excel
            <span className="text-blue-600 block">za pomocą AI</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            <PERSON><PERSON><PERSON>czędź godziny pracy dzięki inteligentnej konwersji dokumentów PDF do formatów Excel i CSV. 
            Bez błędów, bez frustracji, tylko rezultaty.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg" asChild>
              <Link to={isAuthenticated ? "/convert" : "/register"}>
                {isAuthenticated ? "Rozpocznij konwersję" : "Rozpocznij za darmo"}
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
            </Button>
            <Button variant="outline" size="lg" className="px-8 py-4 text-lg" asChild>
              <Link to="/pricing">
                Zobacz cennik
              </Link>
            </Button>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Dlaczego PDFExcelAI?
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Nasza zaawansowana technologia AI zapewnia najwyższą jakość konwersji dokumentów
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                <Zap className="w-6 h-6 text-blue-600" />
              </div>
              <CardTitle>Błyskawiczna konwersja</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">
                Przetwarzamy dokumenty w kilka sekund dzięki zaawansowanym algorytmom AI
              </p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                <Shield className="w-6 h-6 text-green-600" />
              </div>
              <CardTitle>100% bezpieczeństwo</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">
                Twoje pliki są automatycznie usuwane po 24 godzinach. Pełne szyfrowanie SSL
              </p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                <Clock className="w-6 h-6 text-purple-600" />
              </div>
              <CardTitle>Oszczędność czasu</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">
                Zaoszczędź do 15 godzin miesięcznie na ręcznym przetwarzaniu dokumentów
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Social Proof */}
      <div className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-8">
            Zaufało nam już ponad 10,000 użytkowników
          </h3>
          <div className="flex justify-center items-center space-x-2 mb-8">
            {[...Array(5)].map((_, i) => (
              <Star key={i} className="w-6 h-6 text-yellow-400 fill-current" />
            ))}
            <span className="text-gray-600 ml-2">4.9/5 średnia ocena</span>
          </div>
        </div>
      </div>

      {/* Feature Comparison */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <FeatureComparison />
      </div>

      {/* CTA Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 py-16">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-white mb-4">
            Gotowy żeby zacząć?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Dołącz do tysięcy zadowolonych użytkowników już dziś
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 text-lg">
              Rozpocznij za darmo
            </Button>
            <Button variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4 text-lg" asChild>
              <Link to="/dashboard">
                Zobacz panel
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Index;
