import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import FileUpload from '@/components/FileUpload';
import ConversionButton from '@/components/ConversionButton';
import ProgressBar from '@/components/ProgressBar';
import AIProgressBar from '@/components/AIProgressBar';
import ResultsView from '@/components/ResultsView';
import AdvancedSettings from '@/components/AdvancedSettings';
import ProUpgradeModal from '@/components/ProUpgradeModal';
import { useAuth, withAuth } from '@/contexts/AuthContext';
import { ApiClient, Conversion, ConversionOptions } from '@/config/api';
import { toast } from 'sonner';

const ConversionPage = () => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isConverting, setIsConverting] = useState(false);
  const [conversionProgress, setConversionProgress] = useState(0);
  const [currentConversion, setCurrentConversion] = useState<Conversion | null>(null);
  const [conversionResult, setConversionResult] = useState<any>(null);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [conversionOptions, setConversionOptions] = useState<ConversionOptions>({
    outputFormat: 'xlsx',
    extractTables: true,
    extractText: true,
    ocrEnabled: false,
  });

  const { user } = useAuth();
  const navigate = useNavigate();

  // Poll conversion status
  useEffect(() => {
    let pollInterval: NodeJS.Timeout;

    if (currentConversion && isConverting) {
      pollInterval = setInterval(async () => {
        try {
          const updatedConversion = await ApiClient.getConversion(currentConversion.id);
          
          if (updatedConversion.status === 'completed') {
            setIsConverting(false);
            setConversionProgress(100);
            
            // Create mock result data for ResultsView
            const mockResult = [
              ['Kolumna 1', 'Kolumna 2', 'Kolumna 3'],
              ['Dane 1', 'Dane 2', 'Dane 3'],
              ['Dane 4', 'Dane 5', 'Dane 6'],
            ];
            
            setConversionResult(mockResult);
            toast.success('Konwersja zakończona pomyślnie!');
            
          } else if (updatedConversion.status === 'failed') {
            setIsConverting(false);
            setConversionProgress(0);
            toast.error('Konwersja nie powiodła się. Spróbuj ponownie.');
            
          } else if (updatedConversion.status === 'processing') {
            // Simulate progress
            setConversionProgress(prev => Math.min(prev + 10, 90));
          }
          
          setCurrentConversion(updatedConversion);
        } catch (error) {
          console.error('Error polling conversion status:', error);
        }
      }, 2000);
    }

    return () => {
      if (pollInterval) {
        clearInterval(pollInterval);
      }
    };
  }, [currentConversion, isConverting]);

  const handleFileSelect = (file: File | null) => {
    setSelectedFile(file);
    setConversionResult(null);
    setConversionProgress(0);
    setCurrentConversion(null);
  };

  const handleConversion = async () => {
    if (!selectedFile) {
      toast.error('Proszę wybrać plik PDF');
      return;
    }

    // Check if user has reached conversion limit
    if (user?.subscriptionPlan === 'free') {
      try {
        const usage = await ApiClient.getUsageStats();
        if (usage.currentMonth.conversions >= usage.limits.conversions) {
          setShowUpgradeModal(true);
          return;
        }
      } catch (error) {
        console.error('Error checking usage:', error);
      }
    }

    try {
      setIsConverting(true);
      setConversionProgress(10);
      
      const conversion = await ApiClient.uploadFile(selectedFile, conversionOptions);
      setCurrentConversion(conversion);
      
      toast.success('Konwersja rozpoczęta!');
    } catch (error: any) {
      setIsConverting(false);
      setConversionProgress(0);
      
      if (error.response?.status === 429) {
        setShowUpgradeModal(true);
      } else {
        const message = error.response?.data?.message || 'Błąd podczas konwersji';
        toast.error(message);
      }
    }
  };

  const handleReset = () => {
    setSelectedFile(null);
    setConversionResult(null);
    setConversionProgress(0);
    setCurrentConversion(null);
    setIsConverting(false);
  };

  const handleDownload = async () => {
    if (!currentConversion) return;

    try {
      const blob = await ApiClient.downloadConversion(currentConversion.id);
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${selectedFile?.name.replace('.pdf', '')}_converted.xlsx`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      toast.error('Błąd podczas pobierania pliku');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <Button variant="ghost" onClick={() => navigate('/')} className="mb-4">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Powrót do strony głównej
          </Button>
          
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Konwertuj PDF do Excel
          </h1>
          <p className="text-gray-600">
            Prześlij swój plik PDF i przekonwertuj go na format Excel za pomocą AI
          </p>
        </div>

        {/* Main conversion interface */}
        <Card>
          <CardHeader>
            <CardTitle>Konwersja pliku</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* File Upload */}
            <FileUpload
              onFileSelect={handleFileSelect}
              selectedFile={selectedFile}
              disabled={isConverting}
            />

            {/* Advanced Settings */}
            {selectedFile && !isConverting && !conversionResult && (
              <AdvancedSettings
                options={conversionOptions}
                onOptionsChange={setConversionOptions}
              />
            )}

            {/* Conversion Button */}
            {selectedFile && !conversionResult && (
              <ConversionButton
                onClick={handleConversion}
                disabled={!selectedFile || isConverting}
                isConverting={isConverting}
              />
            )}

            {/* Progress */}
            {isConverting && (
              <div className="space-y-4">
                <ProgressBar
                  value={conversionProgress}
                  isConverting={isConverting}
                />
                <AIProgressBar
                  currentStep={conversionProgress < 30 ? 0 : conversionProgress < 60 ? 1 : conversionProgress < 90 ? 2 : 3}
                  isProcessing={isConverting}
                />
              </div>
            )}

            {/* Results */}
            {conversionResult && (
              <ResultsView
                data={conversionResult}
                fileName={selectedFile?.name || 'converted_file'}
                onReset={handleReset}
              />
            )}
          </CardContent>
        </Card>

        {/* Usage info for free users */}
        {user?.subscriptionPlan === 'free' && (
          <Card className="mt-6 border-blue-200 bg-blue-50">
            <CardContent className="pt-4">
              <p className="text-sm text-blue-800">
                <strong>Plan darmowy:</strong> Masz dostęp do 3 konwersji miesięcznie.
                <Button
                  variant="link"
                  className="p-0 h-auto text-blue-600 ml-1"
                  onClick={() => navigate('/pricing')}
                >
                  Sprawdź plany Pro
                </Button>
              </p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Upgrade Modal */}
      <ProUpgradeModal
        isOpen={showUpgradeModal}
        onClose={() => setShowUpgradeModal(false)}
        onUpgrade={() => navigate('/pricing')}
      />
    </div>
  );
};

export default withAuth(ConversionPage);
