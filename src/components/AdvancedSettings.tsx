import React, { useState } from 'react';
import { Setting<PERSON>, <PERSON>lide<PERSON>, <PERSON>, FileText, Zap } from 'lucide-react';
import { ConversionOptions } from '@/config/api';
import { useSubscription } from '@/contexts/AuthContext';

interface AdvancedSettingsProps {
  options: ConversionOptions;
  onOptionsChange: (options: ConversionOptions) => void;
}

const AdvancedSettings: React.FC<AdvancedSettingsProps> = ({
  options,
  onOptionsChange
}) => {
  const { isPro } = useSubscription();

  const updateOption = (key: keyof ConversionOptions, value: any) => {
    onOptionsChange({
      ...options,
      [key]: value,
    });
  };

  const settingsConfig = [
    {
      id: 'extractTables',
      label: 'Wykrywanie tabel',
      description: 'Automatyczne wykrywanie i wyodrębnianie tabel z dokumentu',
      icon: <Sliders className="w-5 h-5" />,
      premium: false,
      value: options.extractTables
    },
    {
      id: 'extractText',
      label: 'Wyodrębnianie tekstu',
      description: 'Wyodrębnienie całego tekstu z dokumentu PDF',
      icon: <FileText className="w-5 h-5" />,
      premium: false,
      value: options.extractText
    },
    {
      id: 'ocrEnabled',
      label: 'OCR (rozpoznawanie tekstu)',
      description: 'Użyj OCR dla zeskanowanych dokumentów i obrazów',
      icon: <Zap className="w-5 h-5" />,
      premium: true,
      value: options.ocrEnabled
    }
  ];

  if (!isPro) {
    return (
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <div className="text-center">
          <div className="mx-auto w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-4">
            <Settings className="w-6 h-6 text-purple-600" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Zaawansowane ustawienia
          </h3>
          <p className="text-gray-600 mb-4">
            Dostępne tylko w wersji PRO
          </p>
          <button className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-2 rounded-lg font-medium hover:shadow-lg transition-all">
            Przejdź na PRO
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl border border-gray-200">
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center">
          <div className="p-2 bg-purple-100 rounded-lg mr-3">
            <Settings className="w-5 h-5 text-purple-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Zaawansowane ustawienia
            </h3>
            <p className="text-sm text-gray-600">
              Dostosuj proces konwersji do swoich potrzeb
            </p>
          </div>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {settingsConfig.map((setting) => (
          <div key={setting.id} className="flex items-start justify-between">
            <div className="flex items-start">
              <div className={`
                p-2 rounded-lg mr-4 mt-1
                ${setting.premium 
                  ? 'bg-purple-100 text-purple-600' 
                  : 'bg-blue-100 text-blue-600'
                }
              `}>
                {setting.icon}
              </div>
              <div className="flex-1">
                <div className="flex items-center mb-1">
                  <h4 className="text-sm font-medium text-gray-900">
                    {setting.label}
                  </h4>
                  {setting.premium && (
                    <span className="ml-2 px-2 py-0.5 bg-purple-100 text-purple-800 text-xs rounded-full font-medium">
                      PRO
                    </span>
                  )}
                </div>
                <p className="text-xs text-gray-600">
                  {setting.description}
                </p>
              </div>
            </div>
            
            <div className="flex-shrink-0 ml-4">
              <button
                onClick={() => handleSettingChange(setting.id, !settings[setting.id as keyof typeof settings])}
                className={`
                  relative inline-flex h-6 w-11 items-center rounded-full transition-colors
                  ${settings[setting.id as keyof typeof settings]
                    ? 'bg-purple-600' 
                    : 'bg-gray-200'
                  }
                `}
              >
                <span
                  className={`
                    inline-block h-4 w-4 transform rounded-full bg-white transition-transform
                    ${settings[setting.id as keyof typeof settings]
                      ? 'translate-x-6' 
                      : 'translate-x-1'
                    }
                  `}
                />
              </button>
            </div>
          </div>
        ))}
      </div>

      <div className="p-6 bg-gradient-to-r from-purple-50 to-pink-50 border-t border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-sm font-medium text-purple-900">
              Profil AI dostosowany do Twoich ustawień
            </h4>
            <p className="text-xs text-purple-700">
              Twoje preferencje są zapisywane automatycznie
            </p>
          </div>
          <div className="flex items-center text-purple-600">
            <Brain className="w-5 h-5 mr-1" />
            <span className="text-sm font-medium">Aktywne</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdvancedSettings;
