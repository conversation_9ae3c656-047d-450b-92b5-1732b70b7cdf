version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:14-alpine
    container_name: pdf-excel-postgres
    environment:
      POSTGRES_DB: pdfexcel
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/migrations:/docker-entrypoint-initdb.d
    networks:
      - pdf-excel-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: pdf-excel-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - pdf-excel-network
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redispassword
    healthcheck:
      test: ["<PERSON><PERSON>", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: pdf-excel-backend
    environment:
      NODE_ENV: development
      PORT: 3001
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USER: postgres
      DB_PASSWORD: password
      DB_NAME: pdfexcel
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: redispassword
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      ENCRYPTION_KEY: your-32-byte-encryption-key-change-in-production
      FRONTEND_URL: http://localhost:8080
    ports:
      - "3001:3001"
    volumes:
      - ./backend:/app
      - /app/node_modules
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    networks:
      - pdf-excel-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    command: npm run dev

  # Frontend (existing Vite dev server)
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: pdf-excel-frontend
    environment:
      VITE_API_URL: http://localhost:3001/api/v1
      VITE_STRIPE_PUBLISHABLE_KEY: pk_test_your_stripe_key
    ports:
      - "8080:8080"
    volumes:
      - .:/app
      - /app/node_modules
    networks:
      - pdf-excel-network
    depends_on:
      - backend
    restart: unless-stopped
    command: npm run dev

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  pdf-excel-network:
    driver: bridge
