# PDF to Excel Backend API

Backend API for the PDF to Excel conversion SaaS application.

## Features

- **Authentication & Authorization**: JWT-based auth with refresh tokens
- **User Management**: Profile management, usage tracking
- **File Upload & Conversion**: PDF upload and conversion to Excel
- **Subscription Management**: Plan management and usage limits
- **Security**: AES-256 encryption, rate limiting, input validation
- **Database**: PostgreSQL with migrations
- **Caching**: Redis for sessions and caching
- **Logging**: Structured logging with Winston

## Tech Stack

- **Runtime**: Node.js 18+
- **Framework**: Express.js with TypeScript
- **Database**: PostgreSQL 14+
- **Cache**: Redis 7+
- **Authentication**: JWT
- **Validation**: Joi + express-validator
- **File Upload**: Multer
- **Security**: Helmet, bcryptjs, rate limiting

## Getting Started

### Prerequisites

- Node.js 18+
- PostgreSQL 14+
- Redis 7+
- Docker (optional)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment setup**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Database setup**
   ```bash
   # Create database
   createdb pdfexcel
   
   # Run migrations
   npm run migrate
   ```

5. **Start development server**
   ```bash
   npm run dev
   ```

### Docker Setup

1. **Using Docker Compose (recommended)**
   ```bash
   # From project root
   docker-compose up -d
   ```

2. **Manual Docker setup**
   ```bash
   # Build image
   docker build -t pdf-excel-backend .
   
   # Run container
   docker run -p 3001:3001 --env-file .env pdf-excel-backend
   ```

## API Documentation

### Authentication Endpoints

- `POST /api/v1/auth/register` - Register new user
- `POST /api/v1/auth/login` - Login user
- `POST /api/v1/auth/refresh` - Refresh access token
- `POST /api/v1/auth/logout` - Logout user
- `POST /api/v1/auth/verify-email` - Verify email
- `POST /api/v1/auth/forgot-password` - Request password reset
- `POST /api/v1/auth/reset-password` - Reset password

### User Endpoints

- `GET /api/v1/users/profile` - Get user profile
- `PUT /api/v1/users/profile` - Update user profile
- `GET /api/v1/users/usage` - Get usage statistics
- `GET /api/v1/users/subscription` - Get subscription details
- `DELETE /api/v1/users/account` - Delete account

### Conversion Endpoints

- `POST /api/v1/conversions/upload` - Upload and convert PDF
- `GET /api/v1/conversions` - Get conversion history
- `GET /api/v1/conversions/:id` - Get conversion details
- `GET /api/v1/conversions/:id/download` - Download converted file
- `DELETE /api/v1/conversions/:id` - Delete conversion
- `POST /api/v1/conversions/:id/retry` - Retry failed conversion

### Subscription Endpoints

- `GET /api/v1/subscriptions/plans` - Get available plans
- `GET /api/v1/subscriptions/current` - Get current subscription
- `POST /api/v1/subscriptions/create-checkout-session` - Create Stripe session
- `POST /api/v1/subscriptions/cancel` - Cancel subscription
- `POST /api/v1/subscriptions/reactivate` - Reactivate subscription

## Environment Variables

```bash
# Server
NODE_ENV=development
PORT=3001

# Database
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=password
DB_NAME=pdfexcel

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=redispassword

# JWT
JWT_SECRET=your-jwt-secret
JWT_REFRESH_SECRET=your-refresh-secret
JWT_ACCESS_EXPIRY=15m
JWT_REFRESH_EXPIRY=7d

# Encryption
ENCRYPTION_KEY=your-32-byte-encryption-key

# File Upload
MAX_FILE_SIZE=52428800
UPLOAD_PATH=./uploads

# Frontend
FRONTEND_URL=http://localhost:8080
```

## Database Schema

### Users Table
- `id` (UUID, Primary Key)
- `email` (VARCHAR, Unique)
- `password_hash` (VARCHAR)
- `first_name` (VARCHAR)
- `last_name` (VARCHAR)
- `subscription_plan` (VARCHAR)
- `is_verified` (BOOLEAN)
- `verification_token` (VARCHAR)
- `reset_token` (VARCHAR)
- `reset_token_expires` (TIMESTAMP)
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

### Conversions Table
- `id` (UUID, Primary Key)
- `user_id` (UUID, Foreign Key)
- `file_name` (VARCHAR)
- `file_size` (INTEGER)
- `file_path` (VARCHAR)
- `processing_time` (INTEGER)
- `status` (VARCHAR)
- `error_message` (TEXT)
- `result_path` (VARCHAR)
- `complexity_score` (DECIMAL)
- `ai_model_used` (VARCHAR)
- `metadata` (JSONB)
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

### Subscriptions Table
- `id` (UUID, Primary Key)
- `user_id` (UUID, Foreign Key)
- `stripe_subscription_id` (VARCHAR)
- `stripe_customer_id` (VARCHAR)
- `plan` (VARCHAR)
- `status` (VARCHAR)
- `current_period_start` (TIMESTAMP)
- `current_period_end` (TIMESTAMP)
- `cancel_at_period_end` (BOOLEAN)
- `trial_end` (TIMESTAMP)
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

### Usage Tracking Table
- `id` (UUID, Primary Key)
- `user_id` (UUID, Foreign Key)
- `month` (VARCHAR)
- `conversions_count` (INTEGER)
- `total_file_size` (BIGINT)
- `total_processing_time` (INTEGER)
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

## Development

### Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm start` - Start production server
- `npm test` - Run tests
- `npm run lint` - Run ESLint
- `npm run migrate` - Run database migrations
- `npm run migrate:rollback` - Rollback migrations

### Testing

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## Security Features

- **Password Hashing**: bcryptjs with salt rounds
- **JWT Tokens**: Access and refresh token system
- **Rate Limiting**: Express rate limit middleware
- **Input Validation**: Joi and express-validator
- **SQL Injection Protection**: Parameterized queries
- **XSS Protection**: Helmet middleware
- **CORS**: Configured for frontend domain
- **File Upload Security**: File type and size validation

## Logging

Logs are written to:
- Console (development)
- `logs/combined.log` (all logs)
- `logs/error.log` (errors only)

## Health Check

The API provides a health check endpoint at `/health` that returns:
- Server status
- Uptime
- Environment
- Timestamp

## Next Steps (Phase 2-6)

1. **Phase 2**: Complete authentication integration with frontend
2. **Phase 3**: Implement real PDF processing with AI models
3. **Phase 4**: Add advanced AI features and caching
4. **Phase 5**: Integrate Stripe payment system
5. **Phase 6**: Add API access and production optimizations
