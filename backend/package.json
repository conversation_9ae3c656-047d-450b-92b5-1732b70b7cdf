{"name": "pdf-excel-backend", "version": "1.0.0", "description": "Backend API for PDF to Excel conversion SaaS", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "migrate": "knex migrate:latest", "migrate:rollback": "knex migrate:rollback", "seed": "knex seed:run"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "pg": "^8.11.3", "knex": "^3.0.1", "redis": "^4.6.10", "multer": "^1.4.5-lts.1", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "crypto": "^1.0.1", "uuid": "^9.0.1", "dotenv": "^16.3.1", "winston": "^3.11.0", "compression": "^1.7.4", "express-slow-down": "^2.0.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/pg": "^8.10.9", "@types/multer": "^1.4.11", "@types/uuid": "^9.0.7", "@types/compression": "^1.7.5", "@types/node": "^20.10.4", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.3.3", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1"}, "engines": {"node": ">=18.0.0"}}