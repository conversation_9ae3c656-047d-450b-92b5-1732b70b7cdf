import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

export interface AppError extends Error {
  statusCode?: number;
  isOperational?: boolean;
  code?: string;
}

export class CustomError extends Error implements AppError {
  statusCode: number;
  isOperational: boolean;
  code?: string;

  constructor(message: string, statusCode: number = 500, isOperational: boolean = true, code?: string) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.code = code;

    Error.captureStackTrace(this, this.constructor);
  }
}

export const createError = (message: string, statusCode: number = 500, code?: string): CustomError => {
  return new CustomError(message, statusCode, true, code);
};

export const errorHandler = (
  error: AppError,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let { statusCode = 500, message } = error;

  // Log error details
  logger.error(`Error ${statusCode}: ${message}`, {
    error: {
      message: error.message,
      stack: error.stack,
      code: error.code
    },
    request: {
      method: req.method,
      url: req.url,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    }
  });

  // Handle specific error types
  if (error.name === 'ValidationError') {
    statusCode = 400;
    message = 'Validation Error';
  } else if (error.name === 'UnauthorizedError' || error.name === 'JsonWebTokenError') {
    statusCode = 401;
    message = 'Unauthorized';
  } else if (error.name === 'CastError') {
    statusCode = 400;
    message = 'Invalid ID format';
  } else if (error.code === '11000') {
    statusCode = 409;
    message = 'Duplicate field value';
  } else if (error.name === 'MulterError') {
    statusCode = 400;
    if (error.code === 'LIMIT_FILE_SIZE') {
      message = 'File too large';
    } else if (error.code === 'LIMIT_FILE_COUNT') {
      message = 'Too many files';
    } else {
      message = 'File upload error';
    }
  }

  // Don't leak error details in production
  if (process.env.NODE_ENV === 'production' && statusCode === 500) {
    message = 'Internal Server Error';
  }

  res.status(statusCode).json({
    success: false,
    error: {
      message,
      ...(process.env.NODE_ENV === 'development' && {
        stack: error.stack,
        code: error.code
      })
    },
    timestamp: new Date().toISOString(),
    path: req.path
  });
};

export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

export const notFound = (req: Request, res: Response, next: NextFunction) => {
  const error = new CustomError(`Not found - ${req.originalUrl}`, 404);
  next(error);
};

// Validation error helper
export const validationError = (message: string, field?: string) => {
  const error = new CustomError(message, 400, true, 'VALIDATION_ERROR');
  if (field) {
    (error as any).field = field;
  }
  return error;
};

// Authentication error helpers
export const unauthorizedError = (message: string = 'Unauthorized') => {
  return new CustomError(message, 401, true, 'UNAUTHORIZED');
};

export const forbiddenError = (message: string = 'Forbidden') => {
  return new CustomError(message, 403, true, 'FORBIDDEN');
};

// Business logic error helpers
export const conflictError = (message: string) => {
  return new CustomError(message, 409, true, 'CONFLICT');
};

export const tooManyRequestsError = (message: string = 'Too many requests') => {
  return new CustomError(message, 429, true, 'TOO_MANY_REQUESTS');
};

export const serviceUnavailableError = (message: string = 'Service temporarily unavailable') => {
  return new CustomError(message, 503, true, 'SERVICE_UNAVAILABLE');
};
