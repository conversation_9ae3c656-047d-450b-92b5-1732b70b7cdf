import { Request, Response, NextFunction } from 'express';
import { AuthService } from '../services/AuthService';
import { CustomError } from './errorHandler';
import { db } from '../config/database';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        firstName?: string;
        lastName?: string;
        subscriptionPlan: string;
        isVerified: boolean;
      };
    }
  }
}

const authService = new AuthService();

/**
 * Middleware to authenticate user using JWT token
 */
export const authenticate = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new CustomError('Access token required', 401, true, 'NO_TOKEN');
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    // Verify token
    const decoded = authService.verifyAccessToken(token);
    
    // Get user from database
    const user = await db('users').where({ id: decoded.userId }).first();
    
    if (!user) {
      throw new CustomError('User not found', 401, true, 'USER_NOT_FOUND');
    }

    // Attach user to request
    req.user = {
      id: user.id,
      email: user.email,
      firstName: user.first_name,
      lastName: user.last_name,
      subscriptionPlan: user.subscription_plan,
      isVerified: user.is_verified
    };

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Middleware to check if user is verified
 */
export const requireVerified = (req: Request, res: Response, next: NextFunction): void => {
  if (!req.user) {
    throw new CustomError('Authentication required', 401, true, 'NOT_AUTHENTICATED');
  }

  if (!req.user.isVerified) {
    throw new CustomError('Email verification required', 403, true, 'EMAIL_NOT_VERIFIED');
  }

  next();
};

/**
 * Middleware to check subscription plan
 */
export const requireSubscription = (allowedPlans: string[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw new CustomError('Authentication required', 401, true, 'NOT_AUTHENTICATED');
    }

    if (!allowedPlans.includes(req.user.subscriptionPlan)) {
      throw new CustomError('Subscription upgrade required', 403, true, 'SUBSCRIPTION_REQUIRED');
    }

    next();
  };
};

/**
 * Middleware to check conversion limits
 */
export const checkConversionLimits = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    if (!req.user) {
      throw new CustomError('Authentication required', 401, true, 'NOT_AUTHENTICATED');
    }

    const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format
    
    // Get current month usage
    const usage = await db('usage_tracking')
      .where({ user_id: req.user.id, month: currentMonth })
      .first();

    const currentUsage = usage ? usage.conversions_count : 0;

    // Define limits based on subscription plan
    const limits = {
      free: 3,
      pro: Infinity,
      business: Infinity
    };

    const userLimit = limits[req.user.subscriptionPlan as keyof typeof limits] || 0;

    if (currentUsage >= userLimit) {
      throw new CustomError('Conversion limit exceeded for your plan', 429, true, 'LIMIT_EXCEEDED');
    }

    // Attach usage info to request for later use
    (req as any).usage = {
      current: currentUsage,
      limit: userLimit,
      month: currentMonth
    };

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Optional authentication middleware (doesn't throw error if no token)
 */
export const optionalAuth = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next();
    }

    const token = authHeader.substring(7);
    
    try {
      const decoded = authService.verifyAccessToken(token);
      const user = await db('users').where({ id: decoded.userId }).first();
      
      if (user) {
        req.user = {
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          subscriptionPlan: user.subscription_plan,
          isVerified: user.is_verified
        };
      }
    } catch (error) {
      // Ignore token errors for optional auth
    }

    next();
  } catch (error) {
    next(error);
  }
};
