import express from 'express';
import { authenticate, requireVerified } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);
router.use(requireVerified);

/**
 * @route   GET /api/v1/subscriptions/plans
 * @desc    Get available subscription plans
 * @access  Private
 */
router.get('/plans',
  asyncHandler(async (req: express.Request, res: express.Response) => {
    const plans = [
      {
        id: 'free',
        name: 'Free',
        price: 0,
        currency: 'PLN',
        interval: 'month',
        features: [
          '3 konwersje miesięcznie',
          'Pliki do 50 MB',
          'Podstawowe szablony',
          'Eksport do Excel'
        ],
        limits: {
          conversions: 3,
          fileSize: 50 * 1024 * 1024
        }
      },
      {
        id: 'pro',
        name: 'Pro',
        price: 19,
        currency: 'PLN',
        interval: 'month',
        features: [
          'Nielimitowane konwersje',
          'Pliki do 200 MB',
          'Zaawansowane AI i autokorekta',
          'Wszystkie szablony',
          'Eksport do CSV i Google Sheets',
          'Priorytetowa obsługa'
        ],
        limits: {
          conversions: Infinity,
          fileSize: 200 * 1024 * 1024
        },
        stripeProductId: process.env.STRIPE_PRO_PRODUCT_ID,
        stripePriceId: process.env.STRIPE_PRO_PRICE_ID
      },
      {
        id: 'business',
        name: 'Business',
        price: 49,
        currency: 'PLN',
        interval: 'month',
        features: [
          'Wszystko z Pro',
          'API dostęp',
          'Masowa konwersja plików',
          'Zaawansowana analityka',
          'Dedykowany menedżer konta',
          'SLA 99.9%'
        ],
        limits: {
          conversions: Infinity,
          fileSize: 500 * 1024 * 1024
        },
        stripeProductId: process.env.STRIPE_BUSINESS_PRODUCT_ID,
        stripePriceId: process.env.STRIPE_BUSINESS_PRICE_ID
      }
    ];

    res.json({
      success: true,
      data: { plans }
    });
  })
);

/**
 * @route   GET /api/v1/subscriptions/current
 * @desc    Get current user subscription
 * @access  Private
 */
router.get('/current',
  asyncHandler(async (req: express.Request, res: express.Response) => {
    // This will be implemented when we add Stripe integration
    res.json({
      success: true,
      data: {
        subscription: {
          plan: req.user!.subscriptionPlan,
          status: 'active',
          currentPeriodEnd: null,
          cancelAtPeriodEnd: false
        }
      }
    });
  })
);

/**
 * @route   POST /api/v1/subscriptions/create-checkout-session
 * @desc    Create Stripe checkout session
 * @access  Private
 */
router.post('/create-checkout-session',
  asyncHandler(async (req: express.Request, res: express.Response) => {
    // This will be implemented in Phase 5 with Stripe integration
    res.status(501).json({
      success: false,
      message: 'Stripe integration not yet implemented'
    });
  })
);

/**
 * @route   POST /api/v1/subscriptions/cancel
 * @desc    Cancel subscription
 * @access  Private
 */
router.post('/cancel',
  asyncHandler(async (req: express.Request, res: express.Response) => {
    // This will be implemented in Phase 5 with Stripe integration
    res.status(501).json({
      success: false,
      message: 'Stripe integration not yet implemented'
    });
  })
);

/**
 * @route   POST /api/v1/subscriptions/reactivate
 * @desc    Reactivate cancelled subscription
 * @access  Private
 */
router.post('/reactivate',
  asyncHandler(async (req: express.Request, res: express.Response) => {
    // This will be implemented in Phase 5 with Stripe integration
    res.status(501).json({
      success: false,
      message: 'Stripe integration not yet implemented'
    });
  })
);

module.exports = router;
