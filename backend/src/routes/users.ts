import express from 'express';
import { body, validationResult } from 'express-validator';
import { authenticate, requireVerified } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';
import { UserService } from '../services/UserService';

const router = express.Router();
const userService = new UserService();

// Apply authentication to all routes
router.use(authenticate);

/**
 * @route   GET /api/v1/users/profile
 * @desc    Get current user profile
 * @access  Private
 */
router.get('/profile', 
  asyncHandler(async (req: express.Request, res: express.Response) => {
    const user = await userService.getProfile(req.user!.id);
    
    res.json({
      success: true,
      data: { user }
    });
  })
);

/**
 * @route   PUT /api/v1/users/profile
 * @desc    Update user profile
 * @access  Private
 */
router.put('/profile',
  [
    body('firstName')
      .optional()
      .isLength({ min: 2, max: 50 })
      .withMessage('First name must be between 2 and 50 characters'),
    body('lastName')
      .optional()
      .isLength({ min: 2, max: 50 })
      .withMessage('Last name must be between 2 and 50 characters'),
    body('email')
      .optional()
      .isEmail()
      .normalizeEmail()
      .withMessage('Please provide a valid email')
  ],
  asyncHandler(async (req: express.Request, res: express.Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { firstName, lastName, email } = req.body;
    
    const user = await userService.updateProfile(req.user!.id, {
      firstName,
      lastName,
      email
    });

    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: { user }
    });
  })
);

/**
 * @route   GET /api/v1/users/usage
 * @desc    Get user usage statistics
 * @access  Private
 */
router.get('/usage',
  asyncHandler(async (req: express.Request, res: express.Response) => {
    const usage = await userService.getUsageStats(req.user!.id);
    
    res.json({
      success: true,
      data: { usage }
    });
  })
);

/**
 * @route   GET /api/v1/users/subscription
 * @desc    Get user subscription details
 * @access  Private
 */
router.get('/subscription',
  asyncHandler(async (req: express.Request, res: express.Response) => {
    const subscription = await userService.getSubscription(req.user!.id);
    
    res.json({
      success: true,
      data: { subscription }
    });
  })
);

/**
 * @route   DELETE /api/v1/users/account
 * @desc    Delete user account
 * @access  Private
 */
router.delete('/account',
  requireVerified,
  body('password').notEmpty().withMessage('Password is required for account deletion'),
  asyncHandler(async (req: express.Request, res: express.Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { password } = req.body;
    
    await userService.deleteAccount(req.user!.id, password);

    res.json({
      success: true,
      message: 'Account deleted successfully'
    });
  })
);

module.exports = router;
