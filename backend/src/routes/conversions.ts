import express from 'express';
import multer from 'multer';
import path from 'path';
import { authenticate, requireVerified, checkConversionLimits } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';
import { ConversionService } from '../services/ConversionService';
import { CustomError } from '../middleware/errorHandler';

const router = express.Router();
const conversionService = new ConversionService();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, process.env.UPLOAD_PATH || './uploads');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE || '52428800'), // 50MB default
    files: 1
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new CustomError('Only PDF files are allowed', 400, true, 'INVALID_FILE_TYPE'));
    }
  }
});

// Apply authentication to all routes
router.use(authenticate);
router.use(requireVerified);

/**
 * @route   POST /api/v1/conversions/upload
 * @desc    Upload and convert PDF file
 * @access  Private
 */
router.post('/upload',
  checkConversionLimits,
  upload.single('pdf'),
  asyncHandler(async (req: express.Request, res: express.Response) => {
    if (!req.file) {
      throw new CustomError('No file uploaded', 400, true, 'NO_FILE');
    }

    const conversion = await conversionService.startConversion({
      userId: req.user!.id,
      file: req.file,
      options: req.body.options ? JSON.parse(req.body.options) : {}
    });

    res.status(202).json({
      success: true,
      message: 'Conversion started',
      data: { conversion }
    });
  })
);

/**
 * @route   GET /api/v1/conversions
 * @desc    Get user's conversion history
 * @access  Private
 */
router.get('/',
  asyncHandler(async (req: express.Request, res: express.Response) => {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const status = req.query.status as string;

    const conversions = await conversionService.getConversions(req.user!.id, {
      page,
      limit,
      status
    });

    res.json({
      success: true,
      data: conversions
    });
  })
);

/**
 * @route   GET /api/v1/conversions/:id
 * @desc    Get specific conversion details
 * @access  Private
 */
router.get('/:id',
  asyncHandler(async (req: express.Request, res: express.Response) => {
    const conversion = await conversionService.getConversion(req.params.id, req.user!.id);

    res.json({
      success: true,
      data: { conversion }
    });
  })
);

/**
 * @route   GET /api/v1/conversions/:id/download
 * @desc    Download converted file
 * @access  Private
 */
router.get('/:id/download',
  asyncHandler(async (req: express.Request, res: express.Response) => {
    const filePath = await conversionService.getDownloadPath(req.params.id, req.user!.id);

    res.download(filePath, (err) => {
      if (err) {
        throw new CustomError('File not found or download failed', 404, true, 'DOWNLOAD_FAILED');
      }
    });
  })
);

/**
 * @route   DELETE /api/v1/conversions/:id
 * @desc    Delete conversion and associated files
 * @access  Private
 */
router.delete('/:id',
  asyncHandler(async (req: express.Request, res: express.Response) => {
    await conversionService.deleteConversion(req.params.id, req.user!.id);

    res.json({
      success: true,
      message: 'Conversion deleted successfully'
    });
  })
);

/**
 * @route   POST /api/v1/conversions/:id/retry
 * @desc    Retry failed conversion
 * @access  Private
 */
router.post('/:id/retry',
  checkConversionLimits,
  asyncHandler(async (req: express.Request, res: express.Response) => {
    const conversion = await conversionService.retryConversion(req.params.id, req.user!.id);

    res.json({
      success: true,
      message: 'Conversion retry started',
      data: { conversion }
    });
  })
);

module.exports = router;
