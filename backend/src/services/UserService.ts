import { db } from '../config/database';
import { EncryptionService } from '../config/security';
import { CustomError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';

export interface UpdateProfileData {
  firstName?: string;
  lastName?: string;
  email?: string;
}

export interface UserProfile {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  subscriptionPlan: string;
  isVerified: boolean;
  createdAt: Date;
}

export interface UsageStats {
  currentMonth: {
    conversions: number;
    totalFileSize: number;
    totalProcessingTime: number;
  };
  allTime: {
    conversions: number;
    totalFileSize: number;
    totalProcessingTime: number;
  };
  limits: {
    conversions: number;
    fileSize: number;
  };
}

export interface SubscriptionDetails {
  plan: string;
  status: string;
  currentPeriodStart?: Date;
  currentPeriodEnd?: Date;
  cancelAtPeriodEnd?: boolean;
  stripeCustomerId?: string;
  stripeSubscriptionId?: string;
}

export class UserService {
  /**
   * Get user profile
   */
  async getProfile(userId: string): Promise<UserProfile> {
    const user = await db('users').where({ id: userId }).first();
    
    if (!user) {
      throw new CustomError('User not found', 404, true, 'USER_NOT_FOUND');
    }

    return {
      id: user.id,
      email: user.email,
      firstName: user.first_name,
      lastName: user.last_name,
      subscriptionPlan: user.subscription_plan,
      isVerified: user.is_verified,
      createdAt: user.created_at
    };
  }

  /**
   * Update user profile
   */
  async updateProfile(userId: string, data: UpdateProfileData): Promise<UserProfile> {
    const updateData: any = {};
    
    if (data.firstName !== undefined) {
      updateData.first_name = data.firstName;
    }
    
    if (data.lastName !== undefined) {
      updateData.last_name = data.lastName;
    }
    
    if (data.email !== undefined) {
      // Check if email is already taken
      const existingUser = await db('users')
        .where({ email: data.email })
        .whereNot({ id: userId })
        .first();
        
      if (existingUser) {
        throw new CustomError('Email already in use', 409, true, 'EMAIL_EXISTS');
      }
      
      updateData.email = data.email;
      updateData.is_verified = false; // Require re-verification for new email
      updateData.verification_token = EncryptionService.generateSecureToken();
    }

    const [updatedUser] = await db('users')
      .where({ id: userId })
      .update(updateData)
      .returning(['id', 'email', 'first_name', 'last_name', 'subscription_plan', 'is_verified', 'created_at']);

    if (!updatedUser) {
      throw new CustomError('User not found', 404, true, 'USER_NOT_FOUND');
    }

    logger.info(`Profile updated for user: ${updatedUser.email}`);

    return {
      id: updatedUser.id,
      email: updatedUser.email,
      firstName: updatedUser.first_name,
      lastName: updatedUser.last_name,
      subscriptionPlan: updatedUser.subscription_plan,
      isVerified: updatedUser.is_verified,
      createdAt: updatedUser.created_at
    };
  }

  /**
   * Get user usage statistics
   */
  async getUsageStats(userId: string): Promise<UsageStats> {
    const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format
    
    // Get current month usage
    const currentMonthUsage = await db('usage_tracking')
      .where({ user_id: userId, month: currentMonth })
      .first();

    // Get all-time usage
    const allTimeUsage = await db('usage_tracking')
      .where({ user_id: userId })
      .sum('conversions_count as totalConversions')
      .sum('total_file_size as totalFileSize')
      .sum('total_processing_time as totalProcessingTime')
      .first();

    // Get user's subscription plan for limits
    const user = await db('users').where({ id: userId }).first();
    
    const limits = this.getSubscriptionLimits(user?.subscription_plan || 'free');

    return {
      currentMonth: {
        conversions: currentMonthUsage?.conversions_count || 0,
        totalFileSize: currentMonthUsage?.total_file_size || 0,
        totalProcessingTime: currentMonthUsage?.total_processing_time || 0
      },
      allTime: {
        conversions: parseInt(allTimeUsage?.totalConversions || '0'),
        totalFileSize: parseInt(allTimeUsage?.totalFileSize || '0'),
        totalProcessingTime: parseInt(allTimeUsage?.totalProcessingTime || '0')
      },
      limits
    };
  }

  /**
   * Get subscription details
   */
  async getSubscription(userId: string): Promise<SubscriptionDetails> {
    const subscription = await db('subscriptions')
      .where({ user_id: userId })
      .orderBy('created_at', 'desc')
      .first();

    if (!subscription) {
      return {
        plan: 'free',
        status: 'active'
      };
    }

    return {
      plan: subscription.plan,
      status: subscription.status,
      currentPeriodStart: subscription.current_period_start,
      currentPeriodEnd: subscription.current_period_end,
      cancelAtPeriodEnd: subscription.cancel_at_period_end,
      stripeCustomerId: subscription.stripe_customer_id,
      stripeSubscriptionId: subscription.stripe_subscription_id
    };
  }

  /**
   * Delete user account
   */
  async deleteAccount(userId: string, password: string): Promise<void> {
    // Verify password
    const user = await db('users').where({ id: userId }).first();
    if (!user) {
      throw new CustomError('User not found', 404, true, 'USER_NOT_FOUND');
    }

    const isPasswordValid = await EncryptionService.comparePassword(password, user.password_hash);
    if (!isPasswordValid) {
      throw new CustomError('Invalid password', 401, true, 'INVALID_PASSWORD');
    }

    // Start transaction
    await db.transaction(async (trx) => {
      // Delete related data
      await trx('usage_tracking').where({ user_id: userId }).del();
      await trx('conversions').where({ user_id: userId }).del();
      await trx('subscriptions').where({ user_id: userId }).del();
      
      // Delete user
      await trx('users').where({ id: userId }).del();
    });

    logger.info(`Account deleted for user: ${user.email}`);
  }

  /**
   * Track usage for billing
   */
  async trackUsage(userId: string, fileSize: number, processingTime: number): Promise<void> {
    const month = new Date().toISOString().slice(0, 7); // YYYY-MM format

    await db('usage_tracking')
      .insert({
        user_id: userId,
        month,
        conversions_count: 1,
        total_file_size: fileSize,
        total_processing_time: processingTime
      })
      .onConflict(['user_id', 'month'])
      .merge({
        conversions_count: db.raw('usage_tracking.conversions_count + 1'),
        total_file_size: db.raw('usage_tracking.total_file_size + ?', [fileSize]),
        total_processing_time: db.raw('usage_tracking.total_processing_time + ?', [processingTime])
      });
  }

  /**
   * Get subscription limits based on plan
   */
  private getSubscriptionLimits(plan: string): { conversions: number; fileSize: number } {
    const limits = {
      free: {
        conversions: 3,
        fileSize: 50 * 1024 * 1024 // 50MB
      },
      pro: {
        conversions: Infinity,
        fileSize: 200 * 1024 * 1024 // 200MB
      },
      business: {
        conversions: Infinity,
        fileSize: 500 * 1024 * 1024 // 500MB
      }
    };

    return limits[plan as keyof typeof limits] || limits.free;
  }
}
