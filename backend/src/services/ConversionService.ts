import { v4 as uuidv4 } from 'uuid';
import fs from 'fs/promises';
import path from 'path';
import { db } from '../config/database';
import { CustomError } from '../middleware/errorHandler';
import { UserService } from './UserService';
import { logger } from '../utils/logger';

export interface ConversionOptions {
  template?: string;
  outputFormat?: 'xlsx' | 'csv';
  extractTables?: boolean;
  extractText?: boolean;
  ocrEnabled?: boolean;
}

export interface ConversionData {
  userId: string;
  file: Express.Multer.File;
  options: ConversionOptions;
}

export interface ConversionResult {
  id: string;
  fileName: string;
  fileSize: number;
  status: string;
  createdAt: Date;
  processingTime?: number;
  resultPath?: string;
}

export interface ConversionsResponse {
  conversions: ConversionResult[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export class ConversionService {
  private userService = new UserService();

  /**
   * Start PDF conversion process
   */
  async startConversion(data: ConversionData): Promise<ConversionResult> {
    const { userId, file, options } = data;

    // Create conversion record
    const conversionId = uuidv4();
    const [conversion] = await db('conversions')
      .insert({
        id: conversionId,
        user_id: userId,
        file_name: file.originalname,
        file_size: file.size,
        file_path: file.path,
        status: 'pending',
        metadata: JSON.stringify(options)
      })
      .returning(['id', 'file_name', 'file_size', 'status', 'created_at']);

    logger.info(`Conversion started: ${conversionId} for user: ${userId}`);

    // Start async processing (this will be replaced with actual AI processing in Phase 3)
    this.processConversionAsync(conversionId, file, options);

    return {
      id: conversion.id,
      fileName: conversion.file_name,
      fileSize: conversion.file_size,
      status: conversion.status,
      createdAt: conversion.created_at
    };
  }

  /**
   * Get user's conversions with pagination
   */
  async getConversions(userId: string, filters: {
    page: number;
    limit: number;
    status?: string;
  }): Promise<ConversionsResponse> {
    const { page, limit, status } = filters;
    const offset = (page - 1) * limit;

    let query = db('conversions')
      .where({ user_id: userId })
      .orderBy('created_at', 'desc');

    if (status) {
      query = query.where({ status });
    }

    // Get total count
    const totalQuery = query.clone();
    const [{ count }] = await totalQuery.count('* as count');
    const total = parseInt(count as string);

    // Get paginated results
    const conversions = await query
      .limit(limit)
      .offset(offset)
      .select(['id', 'file_name', 'file_size', 'status', 'created_at', 'processing_time', 'result_path']);

    return {
      conversions: conversions.map(c => ({
        id: c.id,
        fileName: c.file_name,
        fileSize: c.file_size,
        status: c.status,
        createdAt: c.created_at,
        processingTime: c.processing_time,
        resultPath: c.result_path
      })),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * Get specific conversion
   */
  async getConversion(conversionId: string, userId: string): Promise<ConversionResult> {
    const conversion = await db('conversions')
      .where({ id: conversionId, user_id: userId })
      .first();

    if (!conversion) {
      throw new CustomError('Conversion not found', 404, true, 'CONVERSION_NOT_FOUND');
    }

    return {
      id: conversion.id,
      fileName: conversion.file_name,
      fileSize: conversion.file_size,
      status: conversion.status,
      createdAt: conversion.created_at,
      processingTime: conversion.processing_time,
      resultPath: conversion.result_path
    };
  }

  /**
   * Get download path for converted file
   */
  async getDownloadPath(conversionId: string, userId: string): Promise<string> {
    const conversion = await db('conversions')
      .where({ id: conversionId, user_id: userId })
      .first();

    if (!conversion) {
      throw new CustomError('Conversion not found', 404, true, 'CONVERSION_NOT_FOUND');
    }

    if (conversion.status !== 'completed' || !conversion.result_path) {
      throw new CustomError('Conversion not completed or file not available', 400, true, 'FILE_NOT_READY');
    }

    // Check if file exists
    try {
      await fs.access(conversion.result_path);
      return conversion.result_path;
    } catch (error) {
      throw new CustomError('File not found', 404, true, 'FILE_NOT_FOUND');
    }
  }

  /**
   * Delete conversion and associated files
   */
  async deleteConversion(conversionId: string, userId: string): Promise<void> {
    const conversion = await db('conversions')
      .where({ id: conversionId, user_id: userId })
      .first();

    if (!conversion) {
      throw new CustomError('Conversion not found', 404, true, 'CONVERSION_NOT_FOUND');
    }

    // Delete files
    try {
      await fs.unlink(conversion.file_path);
      if (conversion.result_path) {
        await fs.unlink(conversion.result_path);
      }
    } catch (error) {
      logger.warn(`Failed to delete files for conversion ${conversionId}:`, error);
    }

    // Delete database record
    await db('conversions').where({ id: conversionId }).del();

    logger.info(`Conversion deleted: ${conversionId}`);
  }

  /**
   * Retry failed conversion
   */
  async retryConversion(conversionId: string, userId: string): Promise<ConversionResult> {
    const conversion = await db('conversions')
      .where({ id: conversionId, user_id: userId })
      .first();

    if (!conversion) {
      throw new CustomError('Conversion not found', 404, true, 'CONVERSION_NOT_FOUND');
    }

    if (conversion.status !== 'failed') {
      throw new CustomError('Only failed conversions can be retried', 400, true, 'INVALID_STATUS');
    }

    // Update status to pending
    await db('conversions')
      .where({ id: conversionId })
      .update({
        status: 'pending',
        error_message: null
      });

    // Restart processing
    const file = {
      originalname: conversion.file_name,
      path: conversion.file_path,
      size: conversion.file_size
    } as Express.Multer.File;

    const options = conversion.metadata ? JSON.parse(conversion.metadata) : {};
    this.processConversionAsync(conversionId, file, options);

    logger.info(`Conversion retry started: ${conversionId}`);

    return {
      id: conversion.id,
      fileName: conversion.file_name,
      fileSize: conversion.file_size,
      status: 'pending',
      createdAt: conversion.created_at
    };
  }

  /**
   * Async processing (mockup for now - will be replaced with real AI processing)
   */
  private async processConversionAsync(conversionId: string, file: Express.Multer.File, options: ConversionOptions): Promise<void> {
    try {
      // Update status to processing
      await db('conversions')
        .where({ id: conversionId })
        .update({ status: 'processing' });

      // Simulate processing time
      const processingTime = Math.random() * 5000 + 2000; // 2-7 seconds
      await new Promise(resolve => setTimeout(resolve, processingTime));

      // Create mock result file
      const resultPath = path.join(
        path.dirname(file.path),
        `result-${conversionId}.xlsx`
      );

      // Create a simple mock Excel file (this will be replaced with real conversion)
      const mockExcelContent = 'Mock Excel content - will be replaced with real conversion';
      await fs.writeFile(resultPath, mockExcelContent);

      // Update conversion as completed
      await db('conversions')
        .where({ id: conversionId })
        .update({
          status: 'completed',
          processing_time: Math.round(processingTime),
          result_path: resultPath
        });

      // Track usage
      const conversion = await db('conversions').where({ id: conversionId }).first();
      if (conversion) {
        await this.userService.trackUsage(
          conversion.user_id,
          file.size,
          Math.round(processingTime)
        );
      }

      logger.info(`Conversion completed: ${conversionId}`);

    } catch (error) {
      logger.error(`Conversion failed: ${conversionId}`, error);

      // Update conversion as failed
      await db('conversions')
        .where({ id: conversionId })
        .update({
          status: 'failed',
          error_message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
  }
}
